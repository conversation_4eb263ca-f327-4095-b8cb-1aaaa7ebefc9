<?php
require_once '../config/session.php';
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_customer_status') {
        $customer_id = (int)$_POST['customer_id'];
        $status = sanitize_input($_POST['status']);
        
        try {
            $stmt = $pdo->prepare("UPDATE customers SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $customer_id]);
            
            log_activity($_SESSION['admin_id'], 'customer_status_updated', "Updated customer #$customer_id status to $status");
            $success = 'কাস্টমার স্ট্যাটাস সফলভাবে আপডেট করা হয়েছে।';
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
    
    if ($action === 'delete_customer') {
        $customer_id = (int)$_POST['customer_id'];
        try {
            // Check if customer has orders
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE customer_id = ?");
            $stmt->execute([$customer_id]);
            $orderCount = $stmt->fetchColumn();
            
            if ($orderCount > 0) {
                $error = 'এই কাস্টমারের অর্ডার রয়েছে। কাস্টমার মুছে ফেলা যাবে না।';
            } else {
                $stmt = $pdo->prepare("UPDATE customers SET status = 'inactive' WHERE id = ?");
                $stmt->execute([$customer_id]);
                
                log_activity($_SESSION['admin_id'], 'customer_deleted', "Deleted customer ID: $customer_id");
                $success = 'কাস্টমার সফলভাবে মুছে ফেলা হয়েছে।';
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$sql = "SELECT c.*, 
        (SELECT COUNT(*) FROM orders o WHERE o.customer_id = c.id) as total_orders,
        (SELECT SUM(o.total_amount) FROM orders o WHERE o.customer_id = c.id AND o.payment_status = 'paid') as total_spent,
        (SELECT MAX(o.created_at) FROM orders o WHERE o.customer_id = c.id) as last_order_date
        FROM customers c 
        WHERE 1=1";
$params = [];

if ($status_filter) {
    $sql .= " AND c.status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $sql .= " AND (c.name LIKE ? OR c.email LIKE ? OR c.phone LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$sql .= " ORDER BY c.created_at DESC LIMIT $limit OFFSET $offset";

// Initialize variables
$customers = [];
$totalCustomers = 0;
$totalPages = 0;

try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $customers = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) FROM customers c WHERE 1=1";
    $countParams = [];
    
    if ($status_filter) {
        $countSql .= " AND c.status = ?";
        $countParams[] = $status_filter;
    }
    
    if ($search) {
        $countSql .= " AND (c.name LIKE ? OR c.email LIKE ? OR c.phone LIKE ?)";
        $searchTerm = "%$search%";
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
    }
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $totalCustomers = $countStmt->fetchColumn();
    $totalPages = ceil($totalCustomers / $limit);
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $customers = [];
    $totalCustomers = 0;
    $totalPages = 0;
}

// Status options
$statusOptions = [
    'active' => 'সক্রিয়',
    'inactive' => 'নিষ্ক্রিয়'
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কাস্টমার ব্যবস্থাপনা - অ্যাডমিন প্যানেল</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .filters {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        .customer-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .customer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        .customer-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .info-item {
            display: flex;
            flex-direction: column;
        }
        .info-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.25rem;
        }
        .info-value {
            font-weight: 500;
        }
        .customer-stats {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
        .customer-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .status-select {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            text-decoration: none;
            color: #333;
            border-radius: 4px;
        }
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        .pagination .current {
            background: #667eea;
            color: white;
        }
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
            .filters {
                grid-template-columns: 1fr;
            }
            .customer-info {
                grid-template-columns: 1fr;
            }
            .customer-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            .customer-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
                <p>স্বাগতম, <?php echo $_SESSION['admin_name']; ?></p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="categories.php"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php" class="active"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1>কাস্টমার ব্যবস্থাপনা</h1>
                <div>
                    <span>মোট কাস্টমার: <?php echo $totalCustomers; ?>জন</span>
                </div>
            </header>
            
            <div class="admin-body">
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Filters -->
                <div class="filters">
                    <div class="form-group">
                        <label>কাস্টমার খুঁজুন</label>
                        <input type="text" id="search-input" class="form-control" placeholder="নাম, ইমেইল, ফোন..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="form-group">
                        <label>স্ট্যাটাস</label>
                        <select id="status-filter" class="form-control">
                            <option value="">সব স্ট্যাটাস</option>
                            <?php foreach ($statusOptions as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo $status_filter == $value ? 'selected' : ''; ?>><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search"></i> ফিল্টার করুন
                        </button>
                    </div>
                </div>
                
                <!-- Customers List -->
                <?php if (empty($customers)): ?>
                    <div class="customer-card">
                        <div style="text-align: center; padding: 2rem;">
                            <i class="fas fa-users fa-3x" style="color: #ccc; margin-bottom: 1rem;"></i>
                            <h3>কোন কাস্টমার পাওয়া যায়নি</h3>
                            <p>নির্বাচিত ফিল্টার অনুযায়ী কোন কাস্টমার খুঁজে পাওয়া যায়নি।</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($customers as $customer): ?>
                        <div class="customer-card">
                            <div class="customer-header">
                                <div style="display: flex; align-items: center; gap: 1rem;">
                                    <?php if ($customer['profile_image']): ?>
                                        <img src="../uploads/customers/<?php echo $customer['profile_image']; ?>"
                                             alt="<?php echo $customer['name']; ?>"
                                             style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                                    <?php else: ?>
                                        <div style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.2rem;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <h4><?php echo htmlspecialchars($customer['name']); ?></h4>
                                        <small>যোগদান: <?php echo date('d/m/Y', strtotime($customer['created_at'])); ?></small>
                                    </div>
                                </div>
                                <div>
                                    <span class="badge badge-<?php echo $customer['status']; ?>">
                                        <?php echo $statusOptions[$customer['status']] ?? $customer['status']; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="customer-info">
                                <div class="info-item">
                                    <span class="info-label">ইমেইল</span>
                                    <span class="info-value"><?php echo htmlspecialchars($customer['email']); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">ফোন</span>
                                    <span class="info-value"><?php echo htmlspecialchars($customer['phone']) ?: 'N/A'; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">শহর</span>
                                    <span class="info-value"><?php echo htmlspecialchars($customer['city']) ?: 'N/A'; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">পোস্টাল কোড</span>
                                    <span class="info-value"><?php echo htmlspecialchars($customer['postal_code']) ?: 'N/A'; ?></span>
                                </div>
                            </div>
                            
                            <?php if ($customer['address']): ?>
                                <div style="margin: 1rem 0;">
                                    <span class="info-label">ঠিকানা:</span>
                                    <p style="margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 4px;">
                                        <?php echo nl2br(htmlspecialchars($customer['address'])); ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="customer-stats">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $customer['total_orders'] ?: '0'; ?></div>
                                    <div class="stat-label">মোট অর্ডার</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $customer['total_spent'] ? format_price($customer['total_spent']) : '০ টাকা'; ?></div>
                                    <div class="stat-label">মোট খরচ</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $customer['last_order_date'] ? date('d/m/Y', strtotime($customer['last_order_date'])) : 'কখনো না'; ?></div>
                                    <div class="stat-label">শেষ অর্ডার</div>
                                </div>
                            </div>
                            
                            <div class="customer-actions">
                                <form method="POST" style="display: inline-flex; gap: 0.5rem; align-items: center;">
                                    <input type="hidden" name="action" value="update_customer_status">
                                    <input type="hidden" name="customer_id" value="<?php echo $customer['id']; ?>">
                                    <label>স্ট্যাটাস:</label>
                                    <select name="status" class="status-select" onchange="this.form.submit()">
                                        <?php foreach ($statusOptions as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo $customer['status'] == $value ? 'selected' : ''; ?>><?php echo $label; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </form>
                                
                                <button class="btn btn-secondary btn-sm" onclick="viewCustomerOrders(<?php echo $customer['id']; ?>)">
                                    <i class="fas fa-shopping-cart"></i> অর্ডার দেখুন
                                </button>
                                
                                <button class="btn btn-danger btn-sm" onclick="deleteCustomer(<?php echo $customer['id']; ?>, '<?php echo addslashes($customer['name']); ?>')">
                                    <i class="fas fa-trash"></i> মুছুন
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="<?php echo updateUrlParam('page', $page - 1); ?>">&laquo; পূর্ববর্তী</a>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="<?php echo updateUrlParam('page', $i); ?>"><?php echo $i; ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="<?php echo updateUrlParam('page', $page + 1); ?>">পরবর্তী &raquo;</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/jquery.min.js"></script>
    <script>
        function applyFilters() {
            const search = $('#search-input').val();
            const status = $('#status-filter').val();
            
            let url = 'customers.php?';
            const params = [];
            
            if (search) params.push('search=' + encodeURIComponent(search));
            if (status) params.push('status=' + encodeURIComponent(status));
            
            window.location.href = url + params.join('&');
        }
        
        function viewCustomerOrders(customerId) {
            // This would redirect to orders page filtered by customer
            alert('কাস্টমার অর্ডার দেখার ফিচার শীঘ্রই আসছে!');
        }
        
        function deleteCustomer(id, name) {
            if (confirm('আপনি কি নিশ্চিত যে "' + name + '" কাস্টমারকে মুছে ফেলতে চান?')) {
                const form = $('<form method="POST">' +
                    '<input type="hidden" name="action" value="delete_customer">' +
                    '<input type="hidden" name="customer_id" value="' + id + '">' +
                    '</form>');
                $('body').append(form);
                form.submit();
            }
        }
        
        // Enter key search
        $('#search-input').on('keypress', function(e) {
            if (e.which === 13) {
                applyFilters();
            }
        });
    </script>
</body>
</html>

<?php
function updateUrlParam($param, $value) {
    $params = $_GET;
    $params[$param] = $value;
    return 'customers.php?' . http_build_query($params);
}
?>
