<?php
require_once '../config/session.php';
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_product') {
        $name = sanitize_input($_POST['name']);
        $description = sanitize_input($_POST['description']);
        $price = (float)$_POST['price'];
        $discount_price = !empty($_POST['discount_price']) ? (float)$_POST['discount_price'] : null;
        $category_id = (int)$_POST['category_id'];
        $stock_quantity = (int)$_POST['stock_quantity'];
        $featured = isset($_POST['featured']) ? 1 : 0;
        
        // Handle image upload
        $image_filename = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = upload_image($_FILES['image']);
            if ($upload_result['success']) {
                $image_filename = $upload_result['filename'];
            } else {
                $error = $upload_result['message'];
            }
        }
        
        if (!isset($error)) {
            try {
                $stmt = $pdo->prepare("INSERT INTO products (name, description, price, discount_price, category_id, image, stock_quantity, featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $description, $price, $discount_price, $category_id, $image_filename, $stock_quantity, $featured]);
                
                log_activity($_SESSION['admin_id'], 'product_added', "Added product: $name");
                $success = 'পণ্য সফলভাবে যোগ করা হয়েছে।';
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_product') {
        $product_id = (int)$_POST['product_id'];
        try {
            $stmt = $pdo->prepare("UPDATE products SET status = 'inactive' WHERE id = ?");
            $stmt->execute([$product_id]);
            
            log_activity($_SESSION['admin_id'], 'product_deleted', "Deleted product ID: $product_id");
            $success = 'পণ্য সফলভাবে মুছে ফেলা হয়েছে।';
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}

// Get products
try {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          WHERE p.status = 'active' ORDER BY p.created_at DESC");
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    // Get categories for dropdown
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্য ব্যবস্থাপনা - অ্যাডমিন প্যানেল</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .product-image-thumb {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }
        .actions {
            display: flex;
            gap: 0.5rem;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
                <p>স্বাগতম, <?php echo $_SESSION['admin_name']; ?></p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php" class="active"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="categories.php"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1>পণ্য ব্যবস্থাপনা</h1>
                <button class="btn btn-primary" onclick="openAddProductModal()">
                    <i class="fas fa-plus"></i> নতুন পণ্য যোগ করুন
                </button>
            </header>
            
            <div class="admin-body">
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Products Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-list"></i> পণ্যের তালিকা
                    </div>
                    <div class="card-body">
                        <?php if (empty($products)): ?>
                            <p>কোন পণ্য পাওয়া যায়নি।</p>
                        <?php else: ?>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ছবি</th>
                                        <th>নাম</th>
                                        <th>ক্যাটেগরি</th>
                                        <th>দাম</th>
                                        <th>স্টক</th>
                                        <th>স্ট্যাটাস</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <img src="<?php echo $product['image'] ? '../uploads/products/' . $product['image'] : '../assets/images/no-image.svg'; ?>"
                                                 alt="<?php echo $product['name']; ?>" class="product-image-thumb">
                                        </td>
                                        <td>
                                            <strong><?php echo $product['name']; ?></strong>
                                            <?php if ($product['featured']): ?>
                                                <span class="badge badge-warning">ফিচার্ড</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $product['category_name'] ?? 'N/A'; ?></td>
                                        <td>
                                            <?php if ($product['discount_price']): ?>
                                                <span style="text-decoration: line-through; color: #999;">
                                                    <?php echo format_price($product['price']); ?>
                                                </span><br>
                                                <strong><?php echo format_price($product['discount_price']); ?></strong>
                                            <?php else: ?>
                                                <strong><?php echo format_price($product['price']); ?></strong>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $product['stock_quantity'] <= 10 ? 'badge-warning' : 'badge-confirmed'; ?>">
                                                <?php echo $product['stock_quantity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-confirmed">সক্রিয়</span>
                                        </td>
                                        <td>
                                            <div class="actions">
                                                <button class="btn btn-secondary btn-sm" onclick="editProduct(<?php echo $product['id']; ?>)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-danger btn-sm" onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo $product['name']; ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Add Product Modal -->
    <div id="addProductModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>নতুন পণ্য যোগ করুন</h3>
                <button class="modal-close" onclick="closeAddProductModal()">&times;</button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="addProductForm">
                <input type="hidden" name="action" value="add_product">
                
                <div class="form-group">
                    <label for="name">পণ্যের নাম *</label>
                    <input type="text" name="name" id="name" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="description">বিবরণ</label>
                    <textarea name="description" id="description" class="form-control" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="category_id">ক্যাটেগরি *</label>
                    <select name="category_id" id="category_id" class="form-control" required>
                        <option value="">ক্যাটেগরি নির্বাচন করুন</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="price">দাম (টাকা) *</label>
                    <input type="number" name="price" id="price" class="form-control" step="0.01" min="0" required>
                </div>
                
                <div class="form-group">
                    <label for="discount_price">ছাড়ের দাম (টাকা)</label>
                    <input type="number" name="discount_price" id="discount_price" class="form-control" step="0.01" min="0">
                </div>
                
                <div class="form-group">
                    <label for="stock_quantity">স্টক পরিমাণ *</label>
                    <input type="number" name="stock_quantity" id="stock_quantity" class="form-control" min="0" required>
                </div>
                
                <div class="form-group">
                    <label for="image">পণ্যের ছবি</label>
                    <input type="file" name="image" id="image" class="form-control" accept="image/*">
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="featured" value="1"> ফিচার্ড পণ্য
                    </label>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> সংরক্ষণ করুন
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeAddProductModal()">
                        বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="../assets/js/jquery.min.js"></script>
    <script>
        function openAddProductModal() {
            $('#addProductModal').show();
        }
        
        function closeAddProductModal() {
            $('#addProductModal').hide();
            $('#addProductForm')[0].reset();
        }
        
        function deleteProduct(id, name) {
            if (confirm('আপনি কি নিশ্চিত যে "' + name + '" পণ্যটি মুছে ফেলতে চান?')) {
                const form = $('<form method="POST">' +
                    '<input type="hidden" name="action" value="delete_product">' +
                    '<input type="hidden" name="product_id" value="' + id + '">' +
                    '</form>');
                $('body').append(form);
                form.submit();
            }
        }
        
        function editProduct(id) {
            // This would open an edit modal - implement as needed
            alert('Edit functionality will be implemented');
        }
        
        // Close modal when clicking outside
        $(window).click(function(event) {
            if (event.target.id === 'addProductModal') {
                closeAddProductModal();
            }
        });
    </script>
</body>
</html>
