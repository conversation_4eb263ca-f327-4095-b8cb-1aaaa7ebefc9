<?php
require_once '../config/session.php';
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $full_name = sanitize_input($_POST['full_name']);
        $email = sanitize_input($_POST['email']);
        $username = sanitize_input($_POST['username']);
        
        try {
            // Check if username or email already exists for other users
            $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE (username = ? OR email = ?) AND id != ?");
            $stmt->execute([$username, $email, $_SESSION['admin_id']]);
            if ($stmt->fetch()) {
                $error = 'এই ইউজারনেম বা ইমেইল ইতিমধ্যে ব্যবহৃত হচ্ছে।';
            } else {
                $stmt = $pdo->prepare("UPDATE admin_users SET full_name = ?, email = ?, username = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$full_name, $email, $username, $_SESSION['admin_id']]);
                
                // Update session
                $_SESSION['admin_name'] = $full_name;
                $_SESSION['admin_username'] = $username;
                
                log_activity($_SESSION['admin_id'], 'profile_updated', 'Admin profile updated');
                $success = 'প্রোফাইল সফলভাবে আপডেট করা হয়েছে।';
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
    
    if ($action === 'change_password') {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        if ($new_password !== $confirm_password) {
            $error = 'নতুন পাসওয়ার্ড মিলছে না।';
        } elseif (strlen($new_password) < 6) {
            $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।';
        } else {
            try {
                // Verify current password
                $stmt = $pdo->prepare("SELECT password FROM admin_users WHERE id = ?");
                $stmt->execute([$_SESSION['admin_id']]);
                $admin = $stmt->fetch();
                
                if (!password_verify($current_password, $admin['password'])) {
                    $error = 'বর্তমান পাসওয়ার্ড ভুল।';
                } else {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE admin_users SET password = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$hashed_password, $_SESSION['admin_id']]);
                    
                    log_activity($_SESSION['admin_id'], 'password_changed', 'Admin password changed');
                    $success = 'পাসওয়ার্ড সফলভাবে পরিবর্তন করা হয়েছে।';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'create_admin') {
        $full_name = sanitize_input($_POST['full_name']);
        $email = sanitize_input($_POST['email']);
        $username = sanitize_input($_POST['username']);
        $password = $_POST['password'];
        $role = sanitize_input($_POST['role']);
        
        if (strlen($password) < 6) {
            $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।';
        } else {
            try {
                // Check if username or email already exists
                $stmt = $pdo->prepare("SELECT id FROM admin_users WHERE username = ? OR email = ?");
                $stmt->execute([$username, $email]);
                if ($stmt->fetch()) {
                    $error = 'এই ইউজারনেম বা ইমেইল ইতিমধ্যে ব্যবহৃত হচ্ছে।';
                } else {
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO admin_users (full_name, email, username, password, role) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$full_name, $email, $username, $hashed_password, $role]);
                    
                    log_activity($_SESSION['admin_id'], 'admin_created', "Created new admin: $username");
                    $success = 'নতুন অ্যাডমিন সফলভাবে তৈরি করা হয়েছে।';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
}

// Get current admin info
try {
    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE id = ?");
    $stmt->execute([$_SESSION['admin_id']]);
    $currentAdmin = $stmt->fetch();
    
    // Get all admin users
    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE status = 'active' ORDER BY created_at DESC");
    $stmt->execute();
    $adminUsers = $stmt->fetchAll();
    
    // Get system statistics
    $stats = [];
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE status = 'active'");
    $stats['products'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories WHERE status = 'active'");
    $stats['categories'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM customers WHERE status = 'active'");
    $stats['customers'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
    $stats['orders'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT SUM(total_amount) FROM orders WHERE payment_status = 'paid'");
    $stats['revenue'] = $stmt->fetchColumn() ?: 0;
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $currentAdmin = [];
    $adminUsers = [];
    $stats = [];
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সেটিংস - অ্যাডমিন প্যানেল</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }
        .settings-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .admin-list {
            max-height: 300px;
            overflow-y: auto;
        }
        .admin-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        .admin-item:last-child {
            border-bottom: none;
        }
        .admin-info h5 {
            margin-bottom: 0.25rem;
            color: #333;
        }
        .admin-info small {
            color: #666;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
            .settings-grid {
                grid-template-columns: 1fr;
            }
            .form-row {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
                <p>স্বাগতম, <?php echo $_SESSION['admin_name']; ?></p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="categories.php"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="settings.php" class="active"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1>সিস্টেম সেটিংস</h1>
                <div>
                    <span>অ্যাডমিন: <?php echo $_SESSION['admin_name']; ?></span>
                </div>
            </header>
            
            <div class="admin-body">
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- System Statistics -->
                <div class="settings-section" style="grid-column: 1 / -1; margin-bottom: 2rem;">
                    <h3 class="section-title"><i class="fas fa-chart-bar"></i> সিস্টেম পরিসংখ্যান</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $stats['products'] ?? 0; ?></div>
                            <div class="stat-label">মোট পণ্য</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $stats['categories'] ?? 0; ?></div>
                            <div class="stat-label">ক্যাটেগরি</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $stats['customers'] ?? 0; ?></div>
                            <div class="stat-label">কাস্টমার</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo $stats['orders'] ?? 0; ?></div>
                            <div class="stat-label">অর্ডার</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo format_price($stats['revenue'] ?? 0); ?></div>
                            <div class="stat-label">মোট আয়</div>
                        </div>
                    </div>
                </div>
                
                <div class="settings-grid">
                    <!-- Profile Settings -->
                    <div class="settings-section">
                        <h3 class="section-title"><i class="fas fa-user"></i> প্রোফাইল সেটিংস</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="form-group">
                                <label for="full_name">পূর্ণ নাম</label>
                                <input type="text" name="full_name" id="full_name" class="form-control" 
                                       value="<?php echo htmlspecialchars($currentAdmin['full_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">ইমেইল</label>
                                <input type="email" name="email" id="email" class="form-control" 
                                       value="<?php echo htmlspecialchars($currentAdmin['email'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="username">ইউজারনেম</label>
                                <input type="text" name="username" id="username" class="form-control" 
                                       value="<?php echo htmlspecialchars($currentAdmin['username'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> প্রোফাইল আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Change Password -->
                    <div class="settings-section">
                        <h3 class="section-title"><i class="fas fa-lock"></i> পাসওয়ার্ড পরিবর্তন</h3>
                        <form method="POST">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="form-group">
                                <label for="current_password">বর্তমান পাসওয়ার্ড</label>
                                <input type="password" name="current_password" id="current_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="new_password">নতুন পাসওয়ার্ড</label>
                                <input type="password" name="new_password" id="new_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="confirm_password">নতুন পাসওয়ার্ড নিশ্চিত করুন</label>
                                <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন করুন
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Admin Users -->
                    <div class="settings-section">
                        <h3 class="section-title"><i class="fas fa-users-cog"></i> অ্যাডমিন ব্যবহারকারী</h3>
                        <div class="admin-list">
                            <?php foreach ($adminUsers as $admin): ?>
                                <div class="admin-item">
                                    <div class="admin-info">
                                        <h5><?php echo htmlspecialchars($admin['full_name']); ?></h5>
                                        <small><?php echo htmlspecialchars($admin['username']); ?> (<?php echo $admin['role']; ?>)</small><br>
                                        <small><?php echo htmlspecialchars($admin['email']); ?></small>
                                    </div>
                                    <div>
                                        <span class="badge badge-<?php echo $admin['status']; ?>">
                                            <?php echo $admin['status'] == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <button class="btn btn-secondary" onclick="openCreateAdminModal()" style="margin-top: 1rem;">
                            <i class="fas fa-plus"></i> নতুন অ্যাডমিন যোগ করুন
                        </button>
                    </div>
                    
                    <!-- System Information -->
                    <div class="settings-section">
                        <h3 class="section-title"><i class="fas fa-info-circle"></i> সিস্টেম তথ্য</h3>
                        <div class="info-item" style="margin-bottom: 1rem;">
                            <strong>PHP Version:</strong> <?php echo phpversion(); ?>
                        </div>
                        <div class="info-item" style="margin-bottom: 1rem;">
                            <strong>সিস্টেম:</strong> <?php echo php_uname('s'); ?>
                        </div>
                        <div class="info-item" style="margin-bottom: 1rem;">
                            <strong>সার্ভার সময়:</strong> <?php echo date('Y-m-d H:i:s'); ?>
                        </div>
                        <div class="info-item" style="margin-bottom: 1rem;">
                            <strong>ডাটাবেস:</strong> MySQL
                        </div>
                        <div class="info-item">
                            <strong>সাইট URL:</strong> <?php echo SITE_URL; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Create Admin Modal -->
    <div id="createAdminModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>নতুন অ্যাডমিন তৈরি করুন</h3>
                <button class="modal-close" onclick="closeCreateAdminModal()">&times;</button>
            </div>
            <form method="POST" id="createAdminForm">
                <input type="hidden" name="action" value="create_admin">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="admin_full_name">পূর্ণ নাম *</label>
                        <input type="text" name="full_name" id="admin_full_name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_username">ইউজারনেম *</label>
                        <input type="text" name="username" id="admin_username" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="admin_email">ইমেইল *</label>
                        <input type="email" name="email" id="admin_email" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_role">ভূমিকা</label>
                        <select name="role" id="admin_role" class="form-control">
                            <option value="admin">অ্যাডমিন</option>
                            <option value="manager">ম্যানেজার</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="admin_password">পাসওয়ার্ড *</label>
                    <input type="password" name="password" id="admin_password" class="form-control" required>
                    <small>কমপক্ষে ৬ অক্ষরের হতে হবে</small>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> অ্যাডমিন তৈরি করুন
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeCreateAdminModal()">
                        বাতিল
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script src="../assets/js/jquery.min.js"></script>
    <script>
        function openCreateAdminModal() {
            $('#createAdminModal').show();
        }
        
        function closeCreateAdminModal() {
            $('#createAdminModal').hide();
            $('#createAdminForm')[0].reset();
        }
        
        // Close modal when clicking outside
        $(window).click(function(event) {
            if (event.target.id === 'createAdminModal') {
                closeCreateAdminModal();
            }
        });
        
        // Password confirmation validation
        $('#confirm_password').on('input', function() {
            const newPassword = $('#new_password').val();
            const confirmPassword = $(this).val();
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('পাসওয়ার্ড মিলছে না');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
