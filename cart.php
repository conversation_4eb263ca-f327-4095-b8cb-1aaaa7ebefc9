<?php
require_once 'config/session.php';
require_once 'config/database.php';
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শপিং কার্ট - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .cart-container {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .cart-item {
            display: grid;
            grid-template-columns: 80px 1fr auto auto auto;
            gap: 1rem;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .item-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
        }
        .item-details h4 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        .item-price {
            font-weight: bold;
            color: #667eea;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 3px;
        }
        .quantity-btn:hover {
            background: #f8f9fa;
        }
        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #ddd;
            padding: 5px;
            border-radius: 3px;
        }
        .cart-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .summary-total {
            font-size: 1.2rem;
            font-weight: bold;
            border-top: 1px solid #ddd;
            padding-top: 0.5rem;
            margin-top: 0.5rem;
        }
        .empty-cart {
            text-align: center;
            padding: 3rem;
        }
        .empty-cart i {
            font-size: 4rem;
            color: #ccc;
            margin-bottom: 1rem;
        }
        @media (max-width: 768px) {
            .cart-item {
                grid-template-columns: 60px 1fr;
                gap: 0.5rem;
            }
            .cart-item .quantity-controls,
            .cart-item .item-price,
            .cart-item .remove-btn {
                grid-column: 2;
                justify-self: start;
                margin-top: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> <a href="index.php" style="color: white; text-decoration: none;">আমার দোকান</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="cart.php">কার্ট <span id="cart-count">0</span></a></li>
                    <?php if(isset($_SESSION['customer_id'])): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> প্রোফাইল</a></li>
                        <li><a href="logout.php">লগআউট</a></li>
                    <?php else: ?>
                        <li><a href="login.php">লগইন</a></li>
                        <li><a href="register.php">রেজিস্টার</a></li>
                    <?php endif; ?>
                    <li><a href="admin/login.php">অ্যাডমিন</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="cart-container">
                <h2><i class="fas fa-shopping-cart"></i> আপনার শপিং কার্ট</h2>
                
                <div id="cart-items">
                    <!-- Cart items will be loaded here via JavaScript -->
                </div>
                
                <div id="cart-summary" class="cart-summary" style="display: none;">
                    <div class="summary-row">
                        <span>সাবটোটাল:</span>
                        <span id="subtotal">০ টাকা</span>
                    </div>
                    <div class="summary-row">
                        <span>ডেলিভারি চার্জ:</span>
                        <span id="delivery-charge">৫০ টাকা</span>
                    </div>
                    <div class="summary-row summary-total">
                        <span>মোট:</span>
                        <span id="total">০ টাকা</span>
                    </div>
                    
                    <div style="margin-top: 1.5rem;">
                        <?php if(isset($_SESSION['customer_id'])): ?>
                            <button class="btn btn-primary" style="width: 100%;" onclick="proceedToCheckout()">
                                <i class="fas fa-credit-card"></i> চেকআউট করুন
                            </button>
                        <?php else: ?>
                            <p style="text-align: center; margin-bottom: 1rem;">অর্ডার করতে লগইন করুন</p>
                            <a href="login.php" class="btn btn-primary" style="width: 100%; text-align: center; display: block;">
                                <i class="fas fa-sign-in-alt"></i> লগইন করুন
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div id="empty-cart" class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <h3>আপনার কার্ট খালি</h3>
                    <p>কেনাকাটা শুরু করতে পণ্য যোগ করুন।</p>
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-shopping-bag"></i> কেনাকাটা করুন
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        $(document).ready(function() {
            loadCart();
            updateCartCount();
        });
        
        function loadCart() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            
            if (cart.length === 0) {
                $('#empty-cart').show();
                $('#cart-summary').hide();
                return;
            }
            
            $('#empty-cart').hide();
            $('#cart-summary').show();
            
            let cartHtml = '';
            let subtotal = 0;
            
            cart.forEach(function(item, index) {
                const itemTotal = item.price * item.quantity;
                subtotal += itemTotal;
                
                cartHtml += `
                    <div class="cart-item">
                        <img src="assets/images/no-image.svg" alt="${item.name}" class="item-image">
                        <div class="item-details">
                            <h4>${item.name}</h4>
                            <div class="item-price">${formatPrice(item.price)}</div>
                        </div>
                        <div class="quantity-controls">
                            <button class="quantity-btn" onclick="updateQuantity(${index}, ${item.quantity - 1})">-</button>
                            <input type="number" class="quantity-input" value="${item.quantity}" 
                                   onchange="updateQuantity(${index}, this.value)" min="1">
                            <button class="quantity-btn" onclick="updateQuantity(${index}, ${item.quantity + 1})">+</button>
                        </div>
                        <div class="item-total">
                            <strong>${formatPrice(itemTotal)}</strong>
                        </div>
                        <button class="btn btn-danger btn-sm" onclick="removeFromCartByIndex(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            });
            
            $('#cart-items').html(cartHtml);
            
            const deliveryCharge = subtotal > 0 ? 50 : 0;
            const total = subtotal + deliveryCharge;
            
            $('#subtotal').text(formatPrice(subtotal));
            $('#delivery-charge').text(formatPrice(deliveryCharge));
            $('#total').text(formatPrice(total));
        }
        
        function updateQuantity(index, newQuantity) {
            newQuantity = parseInt(newQuantity);
            if (newQuantity < 1) {
                removeFromCartByIndex(index);
                return;
            }
            
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            if (cart[index]) {
                cart[index].quantity = newQuantity;
                localStorage.setItem('cart', JSON.stringify(cart));
                loadCart();
                updateCartCount();
            }
        }
        
        function removeFromCartByIndex(index) {
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            cart.splice(index, 1);
            localStorage.setItem('cart', JSON.stringify(cart));
            loadCart();
            updateCartCount();
            showAlert('পণ্যটি কার্ট থেকে সরানো হয়েছে।', 'success');
        }
        
        function proceedToCheckout() {
            // This would redirect to checkout page
            alert('চেকআউট পেজ শীঘ্রই আসছে!');
        }
    </script>
</body>
</html>
