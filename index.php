<?php
require_once 'config/session.php';
require_once 'config/database.php';
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অনলাইন দোকান</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> আমার দোকান</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="cart.php">কার্ট <span id="cart-count">0</span></a></li>
                    <?php if(isset($_SESSION['customer_id'])): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> প্রোফাইল</a></li>
                        <li><a href="logout.php">লগআউট</a></li>
                    <?php else: ?>
                        <li><a href="login.php">লগইন</a></li>
                        <li><a href="register.php">রেজিস্টার</a></li>
                    <?php endif; ?>
                    <li><a href="admin/login.php">অ্যাডমিন</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Hero Section -->
            <section class="hero">
                <div class="hero-content">
                    <h2>স্বাগতম আমাদের অনলাইন দোকানে</h2>
                    <p>সেরা মানের পণ্য, সাশ্রয়ী দামে। আমাদের কাছে পাবেন বিশ্বস্ত ও গুণগত পণ্যসামগ্রী।</p>
                    <a href="products.php" class="btn btn-primary">পণ্য দেখুন</a>
                </div>
            </section>

            <!-- Featured Products -->
            <section class="featured-products">
                <h3>বিশেষ পণ্যসমূহ</h3>
                <div class="products-grid" id="featured-products">
                    <!-- Products will be loaded via AJAX -->
                </div>
            </section>

            <!-- Categories -->
            <section class="categories">
                <h3>ক্যাটেগরি</h3>
                <div class="categories-grid" id="categories-list">
                    <!-- Categories will be loaded via AJAX -->
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>যোগাযোগ</h4>
                    <p><i class="fas fa-phone"></i> +880 1234567890</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>সেবা</h4>
                    <ul>
                        <li><a href="#">ডেলিভারি তথ্য</a></li>
                        <li><a href="#">রিটার্ন পলিসি</a></li>
                        <li><a href="#">সাহায্য</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>আমাদের সম্পর্কে</h4>
                    <p>আমরা একটি বিশ্বস্ত অনলাইন দোকান যা গুণগত পণ্য সরবরাহ করে।</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        // Load featured products on page load
        $(document).ready(function() {
            loadFeaturedProducts();
            loadCategories();
            updateCartCount();
        });
    </script>
</body>
</html>
