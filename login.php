<?php
require_once 'config/session.php';
require_once 'config/database.php';

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    redirect('index.php');
}

$error = '';

if ($_POST) {
    $login_input = sanitize_input($_POST['email']); // Can be email or phone
    $password = $_POST['password'];

    if (empty($login_input) || empty($password)) {
        $error = 'ইমেইল/মোবাইল এবং পাসওয়ার্ড আবশ্যক।';
    } else {
        try {
            // Check if input is email or phone
            if (filter_var($login_input, FILTER_VALIDATE_EMAIL)) {
                $stmt = $pdo->prepare("SELECT * FROM customers WHERE email = ? AND status = 'active'");
            } else {
                $stmt = $pdo->prepare("SELECT * FROM customers WHERE phone = ? AND status = 'active'");
            }
            $stmt->execute([$login_input]);
            $customer = $stmt->fetch();
            
            if ($customer && password_verify($password, $customer['password'])) {
                $_SESSION['customer_id'] = $customer['id'];
                $_SESSION['customer_name'] = $customer['name'];
                $_SESSION['customer_email'] = $customer['email'];
                
                // Load cart from database if exists
                $stmt = $pdo->prepare("SELECT p.id, p.name, p.price, c.quantity FROM cart c 
                                      JOIN products p ON c.product_id = p.id 
                                      WHERE c.customer_id = ?");
                $stmt->execute([$customer['id']]);
                $dbCart = $stmt->fetchAll();
                
                if (!empty($dbCart)) {
                    $_SESSION['cart'] = $dbCart;
                }
                
                redirect('index.php');
            } else {
                $error = 'ভুল ইমেইল/মোবাইল বা পাসওয়ার্ড।';
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর। পরে আবার চেষ্টা করুন।';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>লগইন - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .login-container {
            max-width: 400px;
            margin: 3rem auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .social-login {
            margin: 1.5rem 0;
            text-align: center;
        }
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> <a href="index.php" style="color: white; text-decoration: none;">আমার দোকান</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="register.php">রেজিস্টার</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="login-container">
                <div class="login-header">
                    <h2><i class="fas fa-sign-in-alt"></i> লগইন করুন</h2>
                    <p>আপনার অ্যাকাউন্টে প্রবেশ করুন</p>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="loginForm">
                    <div class="form-group">
                        <label for="email">ইমেইল বা মোবাইল নম্বর</label>
                        <input type="text" name="email" id="email" class="form-control"
                               value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                               placeholder="<EMAIL> বা 01XXXXXXXXX" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">পাসওয়ার্ড</label>
                        <input type="password" name="password" id="password" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="remember_me"> আমাকে মনে রাখুন
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-sign-in-alt"></i> লগইন করুন
                        </button>
                    </div>
                </form>
                
                <div class="divider">
                    <span>অথবা</span>
                </div>
                
                <div style="text-align: center;">
                    <p>নতুন ব্যবহারকারী? <a href="register.php">এখানে রেজিস্টার করুন</a></p>
                    <p><a href="forgot_password.php">পাসওয়ার্ড ভুলে গেছেন?</a></p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#loginForm').on('submit', function(e) {
                const loginInput = $('#email').val().trim();
                const password = $('#password').val().trim();

                if (!loginInput || !password) {
                    e.preventDefault();
                    alert('ইমেইল/মোবাইল এবং পাসওয়ার্ড আবশ্যক।');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
