<?php
// Database Migration Script
// This script will update existing database to make phone mandatory and email optional

require_once 'config/database.php';

echo "<h2>ডাটাবেস মাইগ্রেশন</h2>";
echo "<p>কাস্টমার টেবিল আপডেট করা হচ্ছে...</p>";

try {
    // Start transaction
    $pdo->beginTransaction();
    
    // Check if customers table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'customers'");
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ Customers table not found. Please run setup.php first.</p>";
        exit;
    }
    
    echo "<p>✓ Customers table found.</p>";
    
    // Check current table structure
    $stmt = $pdo->query("DESCRIBE customers");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $emailColumn = null;
    $phoneColumn = null;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'email') {
            $emailColumn = $column;
        }
        if ($column['Field'] === 'phone') {
            $phoneColumn = $column;
        }
    }
    
    echo "<p>বর্তমান টেবিল স্ট্রাকচার চেক করা হচ্ছে...</p>";
    
    // Update email column to be nullable
    if ($emailColumn && $emailColumn['Null'] === 'NO') {
        echo "<p>ইমেইল কলাম nullable করা হচ্ছে...</p>";
        $pdo->exec("ALTER TABLE customers MODIFY COLUMN email VARCHAR(100) UNIQUE NULL");
        echo "<p style='color: green;'>✓ ইমেইল কলাম nullable করা হয়েছে।</p>";
    } else {
        echo "<p>✓ ইমেইল কলাম ইতিমধ্যে nullable।</p>";
    }
    
    // Update phone column to be NOT NULL and UNIQUE
    if ($phoneColumn) {
        // First, update any NULL phone values with a temporary value
        $stmt = $pdo->query("SELECT COUNT(*) FROM customers WHERE phone IS NULL OR phone = ''");
        $nullPhoneCount = $stmt->fetchColumn();
        
        if ($nullPhoneCount > 0) {
            echo "<p style='color: orange;'>⚠️ $nullPhoneCount টি কাস্টমারের ফোন নম্বর নেই।</p>";
            echo "<p>এই কাস্টমারদের জন্য temporary phone number সেট করা হচ্ছে...</p>";
            
            // Get customers with null/empty phone
            $stmt = $pdo->query("SELECT id FROM customers WHERE phone IS NULL OR phone = ''");
            $customers = $stmt->fetchAll();
            
            foreach ($customers as $customer) {
                $tempPhone = '01000000' . str_pad($customer['id'], 3, '0', STR_PAD_LEFT);
                $updateStmt = $pdo->prepare("UPDATE customers SET phone = ? WHERE id = ?");
                $updateStmt->execute([$tempPhone, $customer['id']]);
            }
            
            echo "<p style='color: green;'>✓ Temporary phone numbers assigned.</p>";
        }
        
        // Check if phone column is already NOT NULL and UNIQUE
        $stmt = $pdo->query("SHOW INDEX FROM customers WHERE Column_name = 'phone'");
        $phoneIndex = $stmt->fetch();
        
        if ($phoneColumn['Null'] === 'YES' || !$phoneIndex) {
            echo "<p>ফোন কলাম mandatory এবং unique করা হচ্ছে...</p>";
            
            // Remove any duplicate phone numbers first
            $pdo->exec("
                DELETE c1 FROM customers c1
                INNER JOIN customers c2 
                WHERE c1.id > c2.id AND c1.phone = c2.phone
            ");
            
            // Make phone NOT NULL and UNIQUE
            $pdo->exec("ALTER TABLE customers MODIFY COLUMN phone VARCHAR(20) NOT NULL");
            
            // Add unique constraint if not exists
            if (!$phoneIndex) {
                $pdo->exec("ALTER TABLE customers ADD UNIQUE KEY unique_phone (phone)");
            }
            
            echo "<p style='color: green;'>✓ ফোন কলাম mandatory এবং unique করা হয়েছে।</p>";
        } else {
            echo "<p>✓ ফোন কলাম ইতিমধ্যে mandatory এবং unique।</p>";
        }
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo "<h3 style='color: green;'>🎉 মাইগ্রেশন সফল!</h3>";
    echo "<p><strong>পরিবর্তনসমূহ:</strong></p>";
    echo "<ul>";
    echo "<li>✅ ইমেইল এখন optional (nullable)</li>";
    echo "<li>✅ মোবাইল নম্বর এখন mandatory এবং unique</li>";
    echo "<li>✅ লগইন এখন ইমেইল বা মোবাইল দিয়ে করা যাবে</li>";
    echo "</ul>";
    
    echo "<p><strong>এখন আপনি:</strong></p>";
    echo "<ul>";
    echo "<li><a href='register.php'>নতুন রেজিস্ট্রেশন টেস্ট করুন</a> (শুধু মোবাইল দিয়ে)</li>";
    echo "<li><a href='login.php'>লগইন টেস্ট করুন</a> (ইমেইল বা মোবাইল দিয়ে)</li>";
    echo "<li><a href='admin/customers.php'>অ্যাডমিন প্যানেলে কাস্টমার দেখুন</a></li>";
    echo "</ul>";
    
    echo "<p style='color: orange;'><strong>নোট:</strong> মাইগ্রেশন সম্পন্ন হওয়ার পর এই ফাইলটি (migrate_database.php) নিরাপত্তার জন্য মুছে ফেলুন।</p>";
    
} catch(PDOException $e) {
    // Rollback transaction on error
    $pdo->rollback();
    echo "<p style='color: red;'>❌ মাইগ্রেশন এরর: " . $e->getMessage() . "</p>";
    echo "<p>দয়া করে নিশ্চিত করুন যে:</p>";
    echo "<ul>";
    echo "<li>ডাটাবেস সংযোগ সঠিক</li>";
    echo "<li>আপনার ALTER TABLE permission আছে</li>";
    echo "<li>কোন duplicate phone number নেই</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাটাবেস মাইগ্রেশন</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8f9fa;
            line-height: 1.6;
        }
        h2, h3 {
            color: #333;
        }
        p {
            margin: 1rem 0;
        }
        ul {
            margin: 1rem 0;
            padding-left: 2rem;
        }
        a {
            color: #667eea;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
</body>
</html>
