<?php
require_once 'config/session.php';
require_once 'config/database.php';

// Get filter parameters
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : null;
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$sort = isset($_GET['sort']) ? sanitize_input($_GET['sort']) : 'newest';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 12;
$offset = ($page - 1) * $limit;

// Build query
$sql = "SELECT p.*, c.name as category_name FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active'";
$params = [];

if ($category_id) {
    $sql .= " AND p.category_id = ?";
    $params[] = $category_id;
}

if ($search) {
    $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

// Add sorting
switch ($sort) {
    case 'price_low':
        $sql .= " ORDER BY COALESCE(p.discount_price, p.price) ASC";
        break;
    case 'price_high':
        $sql .= " ORDER BY COALESCE(p.discount_price, p.price) DESC";
        break;
    case 'name':
        $sql .= " ORDER BY p.name ASC";
        break;
    default:
        $sql .= " ORDER BY p.created_at DESC";
}

$sql .= " LIMIT $limit OFFSET $offset";

// Initialize variables
$products = [];
$totalProducts = 0;
$totalPages = 0;
$categories = [];

try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) FROM products p WHERE p.status = 'active'";
    $countParams = [];
    
    if ($category_id) {
        $countSql .= " AND p.category_id = ?";
        $countParams[] = $category_id;
    }
    
    if ($search) {
        $countSql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
        $searchTerm = "%$search%";
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
    }
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $totalProducts = $countStmt->fetchColumn();
    $totalPages = ceil($totalProducts / $limit);
    
    // Get categories for filter
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE status = 'active' ORDER BY name ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $products = [];
    $totalProducts = 0;
    $totalPages = 0;
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পণ্যসমূহ - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .products-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        .filters-sidebar {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .products-main {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        .search-box {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
        }
        .search-box input {
            flex: 1;
        }
        .filter-group {
            margin-bottom: 1.5rem;
        }
        .filter-group h4 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        .filter-group ul {
            list-style: none;
        }
        .filter-group li {
            margin-bottom: 0.5rem;
        }
        .filter-group a {
            color: #666;
            text-decoration: none;
            display: block;
            padding: 0.25rem 0;
            transition: color 0.3s ease;
        }
        .filter-group a:hover,
        .filter-group a.active {
            color: #667eea;
            font-weight: 500;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            text-decoration: none;
            color: #333;
            border-radius: 4px;
        }
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        .pagination .current {
            background: #667eea;
            color: white;
        }
        @media (max-width: 768px) {
            .products-layout {
                grid-template-columns: 1fr;
            }
            .filters-sidebar {
                order: 2;
            }
            .products-main {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> <a href="index.php" style="color: white; text-decoration: none;">আমার দোকান</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="cart.php">কার্ট <span id="cart-count">0</span></a></li>
                    <?php if(isset($_SESSION['customer_id'])): ?>
                        <li><a href="profile.php">প্রোফাইল</a></li>
                        <li><a href="logout.php">লগআউট</a></li>
                    <?php else: ?>
                        <li><a href="login.php">লগইন</a></li>
                        <li><a href="register.php">রেজিস্টার</a></li>
                    <?php endif; ?>
                    <li><a href="admin/login.php">অ্যাডমিন</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="products-layout">
                <!-- Filters Sidebar -->
                <aside class="filters-sidebar">
                    <div class="search-box">
                        <input type="text" id="search-input" class="form-control" placeholder="পণ্য খুঁজুন..." value="<?php echo htmlspecialchars($search); ?>">
                        <button class="btn btn-primary" onclick="searchProducts()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    
                    <div class="filter-group">
                        <h4>ক্যাটেগরি</h4>
                        <ul>
                            <li><a href="products.php" class="<?php echo !$category_id ? 'active' : ''; ?>">সব ক্যাটেগরি</a></li>
                            <?php foreach ($categories as $category): ?>
                                <li>
                                    <a href="products.php?category=<?php echo $category['id']; ?>" 
                                       class="<?php echo $category_id == $category['id'] ? 'active' : ''; ?>">
                                        <?php echo $category['name']; ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    
                    <div class="filter-group">
                        <h4>সাজান</h4>
                        <ul>
                            <li><a href="<?php echo updateUrlParam('sort', 'newest'); ?>" class="<?php echo $sort == 'newest' ? 'active' : ''; ?>">নতুন আগে</a></li>
                            <li><a href="<?php echo updateUrlParam('sort', 'price_low'); ?>" class="<?php echo $sort == 'price_low' ? 'active' : ''; ?>">দাম কম থেকে বেশি</a></li>
                            <li><a href="<?php echo updateUrlParam('sort', 'price_high'); ?>" class="<?php echo $sort == 'price_high' ? 'active' : ''; ?>">দাম বেশি থেকে কম</a></li>
                            <li><a href="<?php echo updateUrlParam('sort', 'name'); ?>" class="<?php echo $sort == 'name' ? 'active' : ''; ?>">নাম অনুযায়ী</a></li>
                        </ul>
                    </div>
                </aside>
                
                <!-- Products Main -->
                <main class="products-main">
                    <div class="products-header">
                        <div>
                            <h2>পণ্যসমূহ</h2>
                            <p><?php echo $totalProducts; ?>টি পণ্য পাওয়া গেছে</p>
                        </div>
                    </div>
                    
                    <?php if (isset($error)): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Products Grid -->
                    <div class="products-grid">
                        <?php if (empty($products)): ?>
                            <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                                <i class="fas fa-box-open fa-3x" style="color: #ccc; margin-bottom: 1rem;"></i>
                                <h3>কোন পণ্য পাওয়া যায়নি</h3>
                                <p>অন্য ক্যাটেগরি বা সার্চ টার্ম চেষ্টা করুন।</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($products as $product): ?>
                                <div class="product-card">
                                    <img src="<?php echo $product['image'] ? 'uploads/products/' . $product['image'] : 'assets/images/no-image.svg'; ?>"
                                         alt="<?php echo $product['name']; ?>" class="product-image">
                                    <div class="product-info">
                                        <h4 class="product-name"><?php echo $product['name']; ?></h4>
                                        <div class="product-price">
                                            <?php if ($product['discount_price']): ?>
                                                <?php echo format_price($product['discount_price']); ?>
                                                <span class="original-price"><?php echo format_price($product['price']); ?></span>
                                            <?php else: ?>
                                                <?php echo format_price($product['price']); ?>
                                            <?php endif; ?>
                                        </div>
                                        <p class="product-description">
                                            <?php echo $product['description'] ? substr($product['description'], 0, 100) . '...' : ''; ?>
                                        </p>
                                        <div class="product-actions">
                                            <button class="btn btn-primary" onclick="addToCart(<?php echo $product['id']; ?>, '<?php echo addslashes($product['name']); ?>', <?php echo $product['discount_price'] ?: $product['price']; ?>)">
                                                <i class="fas fa-cart-plus"></i> কার্টে যোগ করুন
                                            </button>
                                            <a href="product_details.php?id=<?php echo $product['id']; ?>" class="btn btn-secondary">
                                                <i class="fas fa-eye"></i> বিস্তারিত
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="<?php echo updateUrlParam('page', $page - 1); ?>">&laquo; পূর্ববর্তী</a>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="<?php echo updateUrlParam('page', $i); ?>"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="<?php echo updateUrlParam('page', $page + 1); ?>">পরবর্তী &raquo;</a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </main>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function searchProducts() {
            const query = $('#search-input').val().trim();
            if (query) {
                window.location.href = 'products.php?search=' + encodeURIComponent(query);
            } else {
                window.location.href = 'products.php';
            }
        }
        
        $('#search-input').on('keypress', function(e) {
            if (e.which === 13) {
                searchProducts();
            }
        });
        
        $(document).ready(function() {
            updateCartCount();
        });
    </script>
</body>
</html>

<?php
function updateUrlParam($param, $value) {
    $params = $_GET;
    $params[$param] = $value;
    return 'products.php?' . http_build_query($params);
}
?>
