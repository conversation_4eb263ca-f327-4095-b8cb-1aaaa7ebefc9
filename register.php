<?php
session_start();
require_once 'config/database.php';

// Redirect if already logged in
if (isset($_SESSION['customer_id'])) {
    redirect('index.php');
}

$error = '';
$success = '';

if ($_POST) {
    $name = sanitize_input($_POST['name']);
    $email = sanitize_input($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $phone = sanitize_input($_POST['phone']);
    $address = sanitize_input($_POST['address']);
    $city = sanitize_input($_POST['city']);
    $postal_code = sanitize_input($_POST['postal_code']);
    
    // Validation
    if (empty($name) || empty($email) || empty($password)) {
        $error = 'নাম, ইমেইল এবং পাসওয়ার্ড আবশ্যক।';
    } elseif ($password !== $confirm_password) {
        $error = 'পাসওয়ার্ড মিলছে না।';
    } elseif (strlen($password) < 6) {
        $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'সঠিক ইমেইল ঠিকানা দিন।';
    } else {
        try {
            // Check if email already exists
            $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                $error = 'এই ইমেইল ঠিকানা দিয়ে ইতিমধ্যে একটি অ্যাকাউন্ট রয়েছে।';
            } else {
                // Create new customer
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO customers (name, email, password, phone, address, city, postal_code) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $email, $hashed_password, $phone, $address, $city, $postal_code]);
                
                $success = 'অ্যাকাউন্ট সফলভাবে তৈরি হয়েছে। এখন লগইন করুন।';
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর। পরে আবার চেষ্টা করুন।';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রেজিস্টার - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .register-container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-store"></i> <a href="index.php" style="color: white; text-decoration: none;">আমার দোকান</a></h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php">হোম</a></li>
                    <li><a href="products.php">পণ্যসমূহ</a></li>
                    <li><a href="login.php">লগইন</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="register-container">
                <div class="register-header">
                    <h2><i class="fas fa-user-plus"></i> নতুন অ্যাকাউন্ট তৈরি করুন</h2>
                    <p>আমাদের সাথে যোগ দিন এবং সহজে কেনাকাটা করুন</p>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        <br><a href="login.php">এখানে লগইন করুন</a>
                    </div>
                <?php endif; ?>
                
                <form method="POST" id="registerForm">
                    <div class="form-group">
                        <label for="name">পূর্ণ নাম *</label>
                        <input type="text" name="name" id="name" class="form-control" 
                               value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">ইমেইল ঠিকানা *</label>
                            <input type="email" name="email" id="email" class="form-control" 
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">ফোন নম্বর</label>
                            <input type="tel" name="phone" id="phone" class="form-control" 
                                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">পাসওয়ার্ড *</label>
                            <input type="password" name="password" id="password" class="form-control" required>
                            <small>কমপক্ষে ৬ অক্ষরের হতে হবে</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">পাসওয়ার্ড নিশ্চিত করুন *</label>
                            <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="address">ঠিকানা</label>
                        <textarea name="address" id="address" class="form-control" rows="2"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="city">শহর</label>
                            <input type="text" name="city" id="city" class="form-control" 
                                   value="<?php echo isset($_POST['city']) ? htmlspecialchars($_POST['city']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="postal_code">পোস্টাল কোড</label>
                            <input type="text" name="postal_code" id="postal_code" class="form-control" 
                                   value="<?php echo isset($_POST['postal_code']) ? htmlspecialchars($_POST['postal_code']) : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-user-plus"></i> অ্যাকাউন্ট তৈরি করুন
                        </button>
                    </div>
                    
                    <div style="text-align: center; margin-top: 1rem;">
                        <p>ইতিমধ্যে অ্যাকাউন্ট আছে? <a href="login.php">এখানে লগইন করুন</a></p>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2024 আমার দোকান। সকল অধিকার সংরক্ষিত।</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#registerForm').on('submit', function(e) {
                const password = $('#password').val();
                const confirmPassword = $('#confirm_password').val();
                
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('পাসওয়ার্ড মিলছে না।');
                    return false;
                }
                
                if (password.length < 6) {
                    e.preventDefault();
                    alert('পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে।');
                    return false;
                }
            });
        });
    </script>
</body>
</html>
